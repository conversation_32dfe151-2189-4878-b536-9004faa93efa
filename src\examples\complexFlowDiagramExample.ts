import { Flow, Node, BatchFlow } from '../pocketflow';
import {generateDiagram} from '../pocketflow/utils/diagramGenerator';

// Define node types
class InputNode extends Node {
  async exec(prepRes: unknown): Promise<string> {
    return "Processing input";
  }
  
  async post(shared: any, prepRes: unknown, execRes: unknown): Promise<string> {
    return shared.condition ? "valid" : "invalid";
  }
}

class ValidateNode extends Node {
  async exec(prepRes: unknown): Promise<string> {
    return "Validating data";
  }
  
  async post(shared: any, prepRes: unknown, execRes: unknown): Promise<string> {
    return Math.random() > 0.3 ? "success" : "retry";
  }
}

class ProcessNode extends Node {
  async exec(prepRes: unknown): Promise<string> {
    return "Processing data";
  }
}

class RetryNode extends Node {
  async exec(prepRes: unknown): Promise<string> {
    return "Retrying operation";
  }
}

class OutputNode extends Node {
  async exec(prepRes: unknown): Promise<string> {
    return "Generating output";
  }
}

class ErrorNode extends Node {
  async exec(prepRes: unknown): Promise<string> {
    return "Handling error";
  }
}

// Create a nested sub-flow for validation
const validateNode = new ValidateNode();
const processNode = new ProcessNode();
const retryNode = new RetryNode();

validateNode.on("success", processNode);
validateNode.on("retry", retryNode);
retryNode.next(validateNode); // Create a loop

const validationFlow = new Flow(validateNode);

// Create the main flow
const inputNode = new InputNode();
const outputNode = new OutputNode();
const errorNode = new ErrorNode();

inputNode.on("valid", validationFlow);
inputNode.on("invalid", errorNode);
validationFlow.next(outputNode);
errorNode.next(outputNode);

const mainFlow = new Flow(inputNode);

// Create a batch flow that runs the main flow multiple times
class BatchProcessor extends BatchFlow {
  async prep(): Promise<Record<string, any>[]> {
    return [
      { condition: true, id: 1 },
      { condition: false, id: 2 },
      { condition: true, id: 3 }
    ];
  }
}

const batchFlow = new BatchProcessor(mainFlow);

// Generate diagrams
async function main() {
  // Generate diagram for the validation sub-flow
  await generateDiagram(validationFlow, './output/validation-flow.png', {
    width: 800,
    height: 600,
    backgroundColor: '#f8f9fa',
    nodeColor: '#e3f2fd',
    edgeColor: '#1976d2',
    textColor: '#212121',
    fontSize: 14
  });
  
  // Generate diagram for the main flow
  await generateDiagram(mainFlow, './output/main-flow.png', {
    width: 1000,
    height: 800,
    backgroundColor: '#f8f9fa',
    nodeColor: '#e8f5e9',
    edgeColor: '#388e3c',
    textColor: '#212121',
    fontSize: 14
  });
  
  // Generate diagram for the batch flow
  await generateDiagram(batchFlow, './output/batch-flow.png', {
    width: 1200,
    height: 1000,
    backgroundColor: '#f8f9fa',
    nodeColor: '#fff3e0',
    edgeColor: '#e65100',
    textColor: '#212121',
    fontSize: 14
  });
  
  console.log('All diagrams generated successfully!');
}

main().catch(console.error);