
import { useParams } from "react-router-dom";
import NavBar from "@/components/NavBar";
import TutorialViewer from "@/components/TutorialViewer";
import { useTutorialDetails } from "@/hooks/useTutorialDetails";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const TutorialDetailsSkeleton = () => (
  <div className="container mx-auto py-6 px-4 max-w-full">
    <div className="mb-6">
      <Skeleton className="h-10 w-3/4 mb-2" />
      <Skeleton className="h-5 w-full mb-2" />
      <Skeleton className="h-5 w-1/2" />
    </div>
    <div className="flex flex-col md:flex-row gap-6">
      <Skeleton className="h-[calc(100vh-200px)] md:w-64" />
      <Skeleton className="h-[calc(100vh-200px)] flex-1 w-full" />
    </div>
  </div>
);

const TutorialDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { tutorial, loading, error } = useTutorialDetails(id || "");

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <NavBar />
        <TutorialDetailsSkeleton />
      </div>
    );
  }

  if (error || !tutorial) {
    return (
      <div className="min-h-screen flex flex-col">
        <NavBar />
        <main className="flex-1 container mx-auto py-12 px-4">
          <Alert variant="destructive" className="max-w-xl mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || "Tutorial not found"}
            </AlertDescription>
          </Alert>
        </main>
      </div>
    );
  }
  console.log("Tutorial:", tutorial);
  const tutorialContent = {
    id: tutorial.id,
    title: tutorial.title,
    description: tutorial.description,
    repoUrl: tutorial.repoUrl,
    chapters: [
      // Add Project Overview as the first item
      {
        id: "project_overview",
        title: `${tutorial.title} Overview`,
        content: tutorial.indexContent || "# Loading content..."
      },
      // Include all other chapters
      ...tutorial.chapters.map(chapter => ({
        id: chapter.filename,
        title: chapter.filename
          .replace(/^\d+_|_/g, ' ')
          .replace(/\.md$/, '')
          .trim()
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' '),
        content: chapter.content || "# Loading content..."
      }))
    ]
  };

  return (
    <div className="min-h-screen flex flex-col">
      <NavBar />
      <div className="container mx-auto py-6 px-4 max-w-full">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">{tutorial.title}</h1>
          <p className="text-muted-foreground mb-2">{tutorial.description}</p>
          {tutorial.repoUrl && (
            <a
              href={tutorial.repoUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-tutorial-primary hover:underline"
            >
              {tutorial.repoUrl}
            </a>
          )}
        </div>
        <TutorialViewer tutorial={tutorialContent} />
      </div>
    </div>
  );
};

export default TutorialDetails;
