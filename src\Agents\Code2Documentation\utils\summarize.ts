// src/utils/summarize.ts
import { callLlm_openrouter } from "@/Agents/shared/callLlm_openrouter";
import type { SharedStore } from "../types";


// Maximum characters per chunk to summarize
const CHUNK_SIZE = 2000;
const SUMMARY_GROUP_SIZE = 5;

/**
 * Summarize an array of code file contents into a single concise summary.
 * Chunks large files and hierarchically combines summaries.
 * @param store SharedStore for LLM calls and caching
 * @param contents Array of code strings
 * @returns Summary string
 */
export async function summarizeCode(
  store: SharedStore,
  contents: string[]
): Promise<string> {
  const chunkSummaries: string[] = [];

  // Summarize each chunk of each file
  for (const content of contents) {
    for (let pos = 0; pos < content.length; pos += CHUNK_SIZE) {
      const chunk = content.slice(pos, pos + CHUNK_SIZE);
      const prompt = `Please provide a concise summary of the following code snippet:\n\`\`\`\n${chunk}\n\`\`\``;
      const summary = await callLlm_openrouter({
        prompt,
        use_cache: store.use_cache,
        temperature: 0.2,
      });
      //const summary = await callLlm( prompt, use_cache: store.use_cache , temperature: 0.2);
      chunkSummaries.push(summary);
    }
  }

  // If only one summary, return directly
  if (chunkSummaries.length === 1) {
    return chunkSummaries[0];
  }

  // Hierarchically combine summaries
  let summaries = chunkSummaries;
  while (summaries.length > 1) {
    const newSummaries: string[] = [];
    for (let i = 0; i < summaries.length; i += SUMMARY_GROUP_SIZE) {
      const group = summaries.slice(i, i + SUMMARY_GROUP_SIZE);
      const prompt = `Combine these summaries into a concise overall summary:\n${group.join("\n")}`;
      const combined = await callLlm( prompt,store.use_cache, 0.2);
      newSummaries.push(combined);
    }
    summaries = newSummaries;
  }

  return summaries[0];
}
