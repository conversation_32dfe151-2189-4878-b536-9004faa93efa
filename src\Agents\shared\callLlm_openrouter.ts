/**
 * Call LLM with caching
 * @param store SharedStore to maintain llmCache
 * @param prompt The prompt string to send
 * @param temperature Sampling temperature
 * @returns Generated text
 */

export async function callLlm_openrouter({
  prompt,
  temperature = 0.2,
  model = "google/gemini-2.5-flash-preview-05-20",
  use_cache = true,
  
}: {
  prompt: string;
  temperature?: number;
  model?: string;
  use_cache?: boolean;
}): Promise<string> {
  // Ensure cache object exists

  const url = "https://openrouter.ai/api/v1/chat/completions";

  const openRouterDevKey =
    "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";
  const options = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${openRouterDevKey}`,

      //'HTTP-Referer': '<YOUR_SITE_URL>', // Optional. Site URL for rankings on openrouter.ai.
      //  'X-Title': '<YOUR_SITE_NAME>', // Optional. Site title for rankings on openrouter.ai.
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature,
      usage: {
        include: true,
      },
    }),
  };
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    //console.log(data);

    const output = data.choices[0].message?.content || "";

    return output;
  } catch (error) {
    console.error(error);

    return error;
  }

  // Cache and return
}
