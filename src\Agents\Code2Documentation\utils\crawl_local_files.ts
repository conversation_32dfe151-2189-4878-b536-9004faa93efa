import fs from 'fs';
import path from 'path';

import ignore from 'ignore';
import { minimatch } from 'minimatch';

export interface LocalCrawlOptions {
  includePatterns?: Set<string>;
  excludePatterns?: Set<string>;
  maxFileSize?: number;
  useRelativePaths?: boolean;  // defaults to true
}

export interface LocalCrawlResult {
  files: Record<string, string>;
}

export async function crawl_local_files(
  directory: string,
  options: LocalCrawlOptions = {}
): Promise<LocalCrawlResult> {
  const {
    includePatterns = new Set<string>(),
    excludePatterns = new Set<string>(),
    maxFileSize,
    useRelativePaths = true,
  } = options;

  // Validate directory
  // const stat = await promisify(fs.stat)(directory).catch(() => null);
  // if (!stat?.isDirectory()) {
  //   throw new Error(`Directory does not exist: ${directory}`);
  // }

  // Load .gitignore
  // const ig = ignore();
  // const gitignorePath = path.join(directory, '.gitignore');
  // if (fs.existsSync(gitignorePath)) {
  //   const content = await promisify(fs.readFile)(gitignorePath, 'utf8');
  //   ig.add(content.split(/\r?\n/));
  // }

  const result: Record<string, string> = {};

  async function walk(dir: string) {
    // const entries = await promisify(fs.readdir)(dir, { withFileTypes: true });
    // for (const entry of entries) {
    //   const full = path.join(dir, entry.name);
    //   const rel = useRelativePaths ? path.relative(directory, full) : full;

    //   // .gitignore exclusion
    //   if (ig.ignores(rel)) continue;

    //   // excludePatterns
    //   if (excludePatterns.size && Array.from(excludePatterns).some(p => minimatch(rel, p))) continue;

    //   if (entry.isDirectory()) {
    //     await walk(full);
    //   } else if (entry.isFile()) {
    //     // includePatterns
    //     const include = includePatterns.size
    //       ? Array.from(includePatterns).some(p => minimatch(rel, p))
    //       : true;
    //     if (!include) continue;

    //     // size check
    //     const fstat = await promisify(fs.stat)(full);
    //     if (maxFileSize && fstat.size > maxFileSize) continue;

    //     // read content
    //     try {
    //       const text = await promisify(fs.readFile)(full, 'utf8');
    //       result[rel] = text;
    //     } catch {
    //       // skip unreadable
    //     }
    //   }
    // }
  }

  await walk(directory);
  return { files: result };
}