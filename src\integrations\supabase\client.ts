// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://axdtrqmggulirxskvwjg.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZHRycW1nZ3VsaXJ4c2t2d2pnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4MTQ2MzEsImV4cCI6MjA2MjM5MDYzMX0.K6tH_zUWz3gFOB9WStMmfDQY8y_jjyi9d-HB4tmSzho";
const SUPABASE_SERVICE_ROLE_KEY ="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZHRycW1nZ3VsaXJ4c2t2d2pnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjgxNDYzMSwiZXhwIjoyMDYyMzkwNjMxfQ.K5cs1WF6A3m_-kBJw1x9fycMG80FTQMrL5lDDA8beUM"  ;
// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);